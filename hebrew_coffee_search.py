#!/usr/bin/env python3
"""
Hebrew Coffee Search Script

This script allows users to input search queries in Hebrew, which are then:
1. Translated and processed by an LLM via OpenRouter.ai
2. Converted into JSON search criteria for the coffee beans API
3. Used to search for matching coffee beans
4. Results are displayed to the user

Requirements:
- OpenRouter.ai API key
- Coffee API key (or uses demo key)
- Internet connection
"""

import json
import requests
import os
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class HebrewCoffeeSearch:
    def __init__(self):
        """Initialize the Hebrew Coffee Search system"""
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.coffee_api_key = os.getenv('COFFEE_API_KEY', 'demo-key-12345-67890-abcdef')
        self.coffee_api_url = os.getenv('COFFEE_API_URL', 'http://localhost:5000/api/search_beans')
        
        # Load available search options from JSON files
        self.load_search_options()
        
        if not self.openrouter_api_key:
            print("⚠️  Warning: OPENROUTER_API_KEY not found in environment variables")
            print("Please set your OpenRouter API key in .env file or environment")
    
    def load_search_options(self):
        """Load available search options from JSON files"""
        try:
            # Load flavors
            with open('ai_dev/coffee_flavors.json', 'r', encoding='utf-8') as f:
                flavors_data = json.load(f)
                self.available_flavors = flavors_data.get('data', [])
            
            # Load countries/origins
            with open('ai_dev/countries.json', 'r', encoding='utf-8') as f:
                countries_data = json.load(f)
                self.available_origins = countries_data.get('data', [])
            
            # Load cities
            with open('ai_dev/city_list.json', 'r', encoding='utf-8') as f:
                cities_data = json.load(f)
                self.available_cities = cities_data.get('data', [])
            
            print(f"✅ Loaded {len(self.available_flavors)} flavors, {len(self.available_origins)} origins, {len(self.available_cities)} cities")
            
        except FileNotFoundError as e:
            print(f"❌ Error loading search options: {e}")
            print("Please ensure the JSON files exist in the ai_dev/ directory")
            self.available_flavors = []
            self.available_origins = []
            self.available_cities = []
    
    def create_search_prompt(self, hebrew_text: str) -> str:
        """Create a prompt for the LLM to convert Hebrew text to search criteria"""
        
        # Create lists for the prompt
        flavors_list = ", ".join(self.available_flavors[:20])  # Show first 20 flavors
        origins_list = ", ".join(self.available_origins[:15])  # Show first 15 origins
        cities_list = ", ".join(self.available_cities[:10])   # Show first 10 cities
        
        prompt = f"""
You are a coffee search expert. Convert the following Hebrew text into JSON search criteria for a coffee beans database.

Hebrew input: "{hebrew_text}"

Available options:
- Flavors (טעמים): {flavors_list}... (and more)
- Origins (מקור): {origins_list}... (and more)  
- Cities (ערים): {cities_list}... (and more)
- Processing methods: Washed, Natural, Honey, Semi-washed
- Roast levels: Light, Medium, Dark, Extra Dark
- Elevation: altd_1 (0-1000m), altd_2 (1001-1500m), altd_3 (1501-2000m), altd_4 (>2000m)
- Brew methods: turkish, espresso, french_press, pour_over, drip, cold_brew

Return ONLY a valid JSON object with these possible fields:
{{
  "origin": [],           // Array of origin countries (in Hebrew)
  "flavors": [],          // Array of flavor notes (in Hebrew)
  "processing": "",       // Single processing method
  "roast_level": "",      // Single roast level
  "elevation": [],        // Array of elevation codes
  "arabica": 0,           // 1 if specifically wants Arabica, 0 otherwise
  "robusta": 0,           // 1 if specifically wants Robusta, 0 otherwise
  "singleorigin": 0,      // 1 if wants single origin, 0 otherwise
  "mix": 0,               // 1 if wants blends, 0 otherwise
  "speciality": 0,        // 1 if wants specialty coffee, 0 otherwise
  "decaf": 0,             // 1 if wants decaf, 0 otherwise
  "brew_methods": [],     // Array of brew methods
  "cities": [],           // Array of Israeli cities (in Hebrew)
  "page": 1,
  "per_page": 10
}}

Guidelines:
- Only include fields that are relevant to the Hebrew input
- Use exact Hebrew terms from the available options
- If user mentions quality/premium/specialty, set speciality: 1
- If user mentions specific brewing method, include in brew_methods
- If user mentions origin countries, include in origin array
- If user mentions flavors/taste notes, include in flavors array
- Set reasonable defaults for page (1) and per_page (10)

Example Hebrew inputs and responses:
"קפה אתיופי עם טעמי פרחים" → {{"origin": ["אתיופיה"], "flavors": ["פרחוני"], "page": 1, "per_page": 10}}
"קפה ספיישלטי לאספרסו" → {{"speciality": 1, "brew_methods": ["espresso"], "page": 1, "per_page": 10}}
"""
        
        return prompt
    
    def query_openrouter(self, prompt: str) -> Optional[Dict]:
        """Query OpenRouter.ai to convert Hebrew text to search criteria"""
        if not self.openrouter_api_key:
            print("❌ OpenRouter API key not available")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.openrouter_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "anthropic/claude-3.5-sonnet",  # Using Claude for better Hebrew support
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.1,  # Low temperature for consistent JSON output
            "max_tokens": 1000
        }
        
        try:
            print("🤖 Querying OpenRouter.ai...")
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content'].strip()
                
                # Try to extract JSON from the response
                try:
                    # Remove any markdown code blocks
                    if content.startswith('```'):
                        content = content.split('```')[1]
                        if content.startswith('json'):
                            content = content[4:]
                    
                    search_criteria = json.loads(content)
                    print("✅ Successfully converted Hebrew text to search criteria")
                    return search_criteria
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Error parsing JSON from LLM response: {e}")
                    print(f"Raw response: {content}")
                    return None
            else:
                print(f"❌ OpenRouter API error: {response.status_code} - {response.text}")
                return None
                
        except requests.RequestException as e:
            print(f"❌ Network error querying OpenRouter: {e}")
            return None
    
    def search_coffee_beans(self, search_criteria: Dict) -> Optional[Dict]:
        """Search for coffee beans using the generated criteria"""
        headers = {
            "Authorization": f"Bearer {self.coffee_api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            print("☕ Searching coffee beans...")
            response = requests.post(
                self.coffee_api_url,
                headers=headers,
                json=search_criteria,
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                print("✅ Successfully retrieved coffee beans")
                return results
            else:
                print(f"❌ Coffee API error: {response.status_code} - {response.text}")
                return None
                
        except requests.RequestException as e:
            print(f"❌ Network error searching coffee beans: {e}")
            return None
    
    def display_results(self, results: Dict, search_criteria: Dict):
        """Display the search results in a user-friendly format"""
        if not results or 'data' not in results:
            print("❌ No results found or invalid response format")
            return
        
        beans = results['data']
        pagination = results.get('pagination', {})
        
        print("\n" + "="*80)
        print("🔍 SEARCH RESULTS")
        print("="*80)
        
        # Display search criteria used
        print("📋 Search criteria used:")
        for key, value in search_criteria.items():
            if value and value != 0 and value != []:
                print(f"   • {key}: {value}")
        
        print(f"\n📊 Found {pagination.get('total', len(beans))} total results")
        print(f"📄 Showing page {pagination.get('page', 1)} of {pagination.get('total_pages', 1)}")
        print("-" * 80)
        
        if not beans:
            print("😔 No coffee beans match your search criteria")
            return
        
        # Display each bean
        for i, bean in enumerate(beans, 1):
            print(f"\n☕ {i}. {bean.get('bean_name', 'Unknown')}")
            print(f"   🏭 Roaster: {bean.get('roaster_name', 'Unknown')}")
            print(f"   🌍 Origin: {bean.get('origin', 'Unknown')}")
            print(f"   🎯 Flavors: {bean.get('flavors', 'Not specified')}")
            print(f"   🔥 Roast Level: {bean.get('roast_level', 'Not specified')}")
            print(f"   💰 Price: ₪{bean.get('price', 'N/A')} ({bean.get('weight', 'N/A')}g)")
            
            if bean.get('arabica'):
                print(f"   🌱 {bean.get('arabica')}% Arabica", end="")
                if bean.get('robusta'):
                    print(f", {bean.get('robusta')}% Robusta")
                else:
                    print()
            
            if bean.get('speciality'):
                print("   ⭐ Specialty Grade")
            
            if bean.get('SCA_score'):
                print(f"   📊 SCA Score: {bean.get('SCA_score')}")
            
            print(f"   🔗 {bean.get('roaster_webpage', 'No website')}")
    
    def run_interactive_search(self):
        """Run the interactive Hebrew coffee search"""
        print("🇮🇱 Hebrew Coffee Search System")
        print("=" * 50)
        print("Enter your coffee search in Hebrew, and I'll find matching beans!")
        print("Examples:")
        print("  • קפה אתיופי עם טעמי פרחים")
        print("  • קפה ספיישלטי לאספרסו")
        print("  • קפה ברזילאי עם טעמי שוקולד")
        print("  • קפה קלוי בינוני מקולומביה")
        print("\nType 'exit' to quit")
        print("-" * 50)
        
        while True:
            try:
                # Get Hebrew input from user
                hebrew_input = input("\n🔍 Enter your search in Hebrew: ").strip()
                
                if hebrew_input.lower() in ['exit', 'quit', 'יציאה']:
                    print("👋 Goodbye!")
                    break
                
                if not hebrew_input:
                    print("⚠️  Please enter a search query")
                    continue
                
                # Step 1: Convert Hebrew to search criteria using LLM
                prompt = self.create_search_prompt(hebrew_input)
                search_criteria = self.query_openrouter(prompt)
                
                if not search_criteria:
                    print("❌ Failed to process your search. Please try again.")
                    continue
                
                # Step 2: Search for coffee beans
                results = self.search_coffee_beans(search_criteria)
                
                if not results:
                    print("❌ Failed to search coffee beans. Please try again.")
                    continue
                
                # Step 3: Display results
                self.display_results(results, search_criteria)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                print("Please try again.")

def main():
    """Main function to run the Hebrew Coffee Search"""
    search_system = HebrewCoffeeSearch()
    search_system.run_interactive_search()

if __name__ == "__main__":
    main()
