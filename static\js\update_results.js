// Helper function to get cookie by name
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

let currentSort = { field: null, direction: 'asc' };
let currentPage = 1;
let currentFilters = null;
let totalResults = 0;

function sortBy(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }
    
    // Reset pagination when sorting
    currentPage = 1;
    
    // Clear existing results and reload with new sort
    const beanList = document.getElementById('beanList');
    if (beanList) beanList.innerHTML = '';
    
    window.externalUpdateResults();
    updateSortArrows();
}

// For backward compatibility
function updateResults() {
    window.externalUpdateResults();
}

// Export the function to the global scope so it can be called from inline scripts
window.externalUpdateResults = async function() {

    const formData = new FormData(document.querySelector('form'));
    const data = Object.fromEntries(formData.entries());

    // Add pagination parameter - limit non-authenticated users to 5 results
    data.per_page = window.userLoggedIn ? 20 : 5;
    data.page = 1;
    
    // Use global arrays directly instead of form data to ensure proper deselection handling
    data.origin = typeof selectedOrigins !== 'undefined' ? selectedOrigins : [];
    data.flavors = typeof selectedFlavors !== 'undefined' ? selectedFlavors : [];
    
    // Ensure arrays are not empty strings
    if (typeof data.origin === 'string' && data.origin === '') data.origin = [];
    if (typeof data.flavors === 'string' && data.flavors === '') data.flavors = [];

    // Get state from buttons
    const arabicaBtn = document.getElementById('arabicaBtn');
    const robustaBtn = document.getElementById('robustaBtn');
    const singOBtn = document.getElementById('singOBtn'); // Add single origin button reference
    const mixBtn = document.getElementById('mixBtn');
    const specialityBtn = document.getElementById('specialityBtn');
    const decafBtn = document.getElementById('decafBtn');
    const israBtn = document.getElementById('israBtn'); // Israeli content button (may not exist)
 
     data.arabica = arabicaBtn && arabicaBtn.classList.contains('active') ? 1 : 0;
    data.robusta = robustaBtn && robustaBtn.classList.contains('active') ? 1 : 0;
    data.singleorigin = singOBtn && singOBtn.classList.contains('active') ? 1 : 0; // Add single origin flag
    data.mix = mixBtn && mixBtn.classList.contains('active') ? 1 : 0;
    data.speciality = specialityBtn && specialityBtn.classList.contains('active') ? 1 : 0;
    data.decaf = decafBtn && decafBtn.classList.contains('active') ? 1 : 0;
    data.isra_content = israBtn && israBtn.classList.contains('active') ? 1 : 0;
 
    // Get elevation value
    const elevationSelect = document.getElementById('elevation');
    data.elevation = elevationSelect && elevationSelect.value ? [elevationSelect.value] : [];

    // Get processing value - clear if empty
    const processingSelect = document.getElementById('processing');
    data.processing = processingSelect && processingSelect.value ? processingSelect.value : '';

    // Get roast level value - clear if empty  
    const roastLevelSelect = document.getElementById('roast_level');
    data.roast_level = roastLevelSelect && roastLevelSelect.value ? roastLevelSelect.value : '';

    // Include roaster search criteria from the global selectedRoasters array
    data.roasters = typeof selectedRoasters !== 'undefined' ? selectedRoasters.map(r => r.id) : [];

    // Include cities from global selectedCities array
    data.cities = typeof selectedCities !== 'undefined' ? selectedCities : [];

    // Collect brew methods from checkboxes
    const brewFilters = document.querySelectorAll('.brew-filter:checked');
    data.brew_methods = Array.from(brewFilters).map(cb => cb.value);
    
    // Check if all brew methods are selected (which is the same as no filter)
    const allBrewFilters = document.querySelectorAll('.brew-filter');
    const allBrewMethodsSelected = brewFilters.length === allBrewFilters.length;

    data.flavor_mode = window.flavorMode;



    // If no filters are applied, clear table and skip fetch
    const beanList = document.getElementById('beanList');
    if (
        (!data.origin || data.origin.length === 0) &&
        (!data.flavors || data.flavors.length === 0) &&
        (!data.processing || data.processing === '') &&
        (!data.roast_level || data.roast_level === '') &&
        (!data.elevation || data.elevation.length === 0) && // Check if elevation array is empty
        data.arabica === 0 &&
        data.robusta === 0 &&
        data.singleorigin === 0 && // Include single origin check
        data.mix === 0 &&
        data.speciality === 0 &&
        data.decaf === 0 &&
        (allBrewMethodsSelected || !data.brew_methods || data.brew_methods.length === 0) && // Check brew methods - treat all selected as no filter
        (!data.roasters || data.roasters.length === 0) &&
        (!data.cities || data.cities.length === 0) &&
        data.isra_content === 0
     ) {
         if (beanList) {
            beanList.innerHTML = ''; // Clear table
        }
        removeLoadMoreButton(); // Remove load more button when no filters
        return; // Skip fetching
    }

    // Remove or update header for SCA Score as needed
    const tableHeadRow = document.querySelector('table thead tr');
    const existingSCAHeader = document.getElementById('scaScoreHeader');
    if (existingSCAHeader) {
        existingSCAHeader.remove();
    }
    if (data.speciality == 1) {
        const specialityHeader = document.getElementById('specialityHeader');
        if (specialityHeader) {
            const scaHeader = document.createElement('th');
            scaHeader.id = 'scaScoreHeader';
            scaHeader.classList.add('title');
            scaHeader.textContent = 'SCA Score';
            specialityHeader.parentNode.insertBefore(scaHeader, specialityHeader.nextSibling);
        }
    }
    const scaCol = document.getElementById('scaCol');
    if (scaCol) {
        scaCol.style.display = data.speciality == 1 ? 'table-column' : 'none';
    }

    data.sort_by = currentSort.field;
    data.sort_dir = currentSort.direction;

    // Store current speciality filter state globally
    window.currentSpecialityActive = (data.speciality == 1);

    try {
        // Get API key from meta tag or use demo key
        let apiKey = null;
        const apiKeyMeta = document.querySelector('meta[name="api-key"]');
        if (apiKeyMeta) {
            apiKey = apiKeyMeta.getAttribute('content');
        } else {
            // Use demo API key if no meta tag found
            apiKey = 'demo-key-12345-67890-abcdef';
        }

        const response = await fetch('/api/search_beans', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const response_data = await response.json();
        if (!beanList) return;
        
        // Handle the new API response structure
        const beans = response_data.data || response_data;
        currentFilters = data; // Store current filters for load more
        
        if (beans.length === 0) {
            beanList.innerHTML = '<tr><td colspan="21">לא נמצאו פולים התואמים לסינון המבוקש.</td></tr>';
            removeLoadMoreButton();
            return;
        }
        
        // For initial load, replace content
        displayBeans(beans, true);
        
        // Check if there might be more results (if we got exactly per_page results)
        // But don't show load more button for non-authenticated users
        if (beans.length === parseInt(data.per_page) && window.userLoggedIn) {
            showLoadMoreButton();
        } else {
            removeLoadMoreButton();
        }

        // Ensure the first cell in each row has the sticky-col class
        beanList.querySelectorAll('tr').forEach(function(row) {
            let firstCell = row.querySelector('td:first-child');
            if (firstCell) {
                firstCell.classList.add('sticky-col');
            }
        });
    } catch (error) {
        console.error('Error fetching beans:', error);
        if (beanList) {
            beanList.innerHTML = `<tr><td colspan="21">שגיאה בטעינת הפולים: ${error.message}</td></tr>`;
        }
    }

    // Ensure the first cell in each row has the sticky class
    beanList.querySelectorAll('tr').forEach(function(row) {
        let firstCell = row.querySelector('td:first-child');
        if (firstCell) {
            firstCell.classList.add('sticky-col');
        }
    });
}

function displayBeans(beans, clearTable = false) {
    const beanList = document.getElementById('beanList');
    if (clearTable) {
        beanList.innerHTML = '';
        currentPage = 1;
    }

    // Server-side sorting is now handled - no client-side sorting needed

    beans.forEach(bean => {
        const weightDisplay = bean.weight == 1000 ? "1 ק\"ג" : (bean.weight || '');
        const specialityDisplay = (bean.speciality === 1 || bean.speciality === true) ? '✓' : '';
        let scaCell = "";
        if (window.currentSpecialityActive) {
            scaCell = `<td>${bean.SCA_score || ''}</td>`;
        }
        
        // Use server-provided price100 or calculate as fallback
        let price100 = "";
        if (bean.price100) {
            price100 = bean.price100;
        } else if (bean.price && bean.weight) {
            price100 = (bean.price / (bean.weight / 100)).toFixed(2);
        }
        
        // Create bean name display with optional isra_content text
        let beanNameDisplay = bean.bean_name || '';
        if (bean.isra_content === true || bean.isra_content === 1) {
            beanNameDisplay += '<br><small style="color: #d4a853; font-weight: bold;">הקפה הטוב ביותר 2025</small>';
        }
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${beanNameDisplay}</td>
            <td><span class="roaster-name" onclick="showRoasterDetails(${bean.roaster_id})">${bean.roaster_name || ''}</span></td>
            <td>${bean.origin || ''}</td>
            <td>${bean.processing || ''}<br>${bean.elevation || ''}</td>
            <td>${bean.flavors || ''}</td>
            <td>${bean.roast_level || ''}</td>
            <td>${bean.body || ''}</td>
            <td>${bean.acidity || ''}</td>
            <td>${bean.arabica || ''}</td>
            <td>${bean.robusta || ''}</td>
            <td>${bean.mix ? 'תערובת' : 'חד-זני'}</td>
            <td>${specialityDisplay}</td>
            ${scaCell}
            <td>${(bean.price || '')}<br>${weightDisplay}</td>
            <td>${price100}</td>
            <td>${bean.turkish ? '✓' : ''}</td>
            <td>${bean.espresso ? '✓' : ''}</td>
            <td>${bean.french_press ? '✓' : ''}</td>
            <td>${bean.pour_over ? '✓' : ''}</td>
            <td>${bean.drip ? '✓' : ''}</td>
            <td>${bean.cold_brew ? '✓' : ''}</td>
            <td></td>
        `;

        // Create and append the brew button safely
        const brewButtonCell = row.cells[row.cells.length - 1]; // Get the last cell

        // Check if user is logged in (check for global variable or session)
        const userLoggedIn = window.userLoggedIn !== undefined ? window.userLoggedIn : false;

        if (userLoggedIn) {
            const brewButton = document.createElement('button');
            brewButton.className = 'brew-button';
            brewButton.textContent = 'חליטה';
            // Use addEventListener to avoid escaping issues
            brewButton.addEventListener('click', function() {
                // Pass bean_name directly as a variable
                openBrewModal(bean.bean_id, bean.bean_name);
            });
            brewButtonCell.appendChild(brewButton);
        } else {
            // For unregistered users, show disabled text or login prompt
            const loginPrompt = document.createElement('span');
            loginPrompt.textContent = 'התחבר לשמירה';
            loginPrompt.style.color = '#666';
            loginPrompt.style.fontSize = '12px';
            loginPrompt.style.cursor = 'pointer';
            loginPrompt.addEventListener('click', function() {
                // Open login modal
                const loginModal = document.getElementById('loginModal');
                if (loginModal) {
                    loginModal.style.display = 'flex';
                }
            });
            brewButtonCell.appendChild(loginPrompt);
        }

        beanList.appendChild(row);
    });
}

function showRoasterDetails(roasterId) {
    console.log('Fetching details for roaster:', roasterId);
    const content = document.getElementById('roasterDetailContent');
    content.innerHTML = 'טוען...';
    document.getElementById('roasterDetailModal').style.display = 'block';

    fetch(`/get_roaster_details/${roasterId}`)
        .then(response => {
            if (!response.ok) {
                // If we get a 401 (Unauthorized) or 403 (Forbidden), it means the user is not logged in
                if (response.status === 401 || response.status === 403) {
                    throw new Error('יש להתחבר למערכת כדי לצפות בפרטי הקולה המלאים');
                }
                return response.text().then(text => {
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data && data.name) {
                content.innerHTML = `
                    <h2 style="font-size: 1.5em;">${data.website && data.website !== 'לא זמין' ? `<a href="${data.website}" target="_blank" style="text-decoration: underline; color: blue;">${data.name}</a>` : data.name}</h2>
                    <p><strong>כתובת:</strong> ${data.address || 'לא זמין'}</p>
                    <p><strong>עיר:</strong> ${data.city || 'לא זמין'}</p>
                    <p><strong>מיקוד:</strong> ${data.zip || 'לא זמין'}</p>
                    <p><strong>דוא"ל:</strong> ${data.email || 'לא זמין'}</p>
                    <p><strong>משלוח חינם מעל:</strong> ${data.minimum_shipping || 'לא זמין'}</p>
                    <p><strong>דמי משלוח:</strong> ${data.shipping_cost || 'לא זמין'}</p>
                `;
            } else {
                content.innerHTML = `<p style="color: red;">לא נמצאו פרטים עבור קולה זו.</p>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = `<p style="color: red;">שגיאה בטעינת פרטי הקולה: ${error.message}</p>`;
        });
}

function updateSortArrows() {
    // Clear all arrows first
    const sortArrows = ['price100SortArrow', 'arabicaSortArrow', 'robustaSortArrow', 'bean_nameSortArrow', 'roaster_nameSortArrow', 'priceSortArrow'];
    sortArrows.forEach(arrowId => {
        const arrow = document.getElementById(arrowId);
        if (arrow) arrow.textContent = '';
    });

    // Set arrow for current sort field
    const currentArrow = document.getElementById(currentSort.field + 'SortArrow');
    if (currentArrow) {
        currentArrow.textContent = (currentSort.direction === 'asc') ? '▲' : '▼';
    }
}

function showLoadMoreButton() {
    removeLoadMoreButton(); // Remove existing button if any
    
    const tableContainer = document.querySelector('.table-container') || document.querySelector('table').parentNode;
    const loadMoreBtn = document.createElement('button');
    loadMoreBtn.id = 'loadMoreBtn';
    loadMoreBtn.className = 'load-more-btn';
    loadMoreBtn.textContent = 'טען פולים נוספים';
    loadMoreBtn.style.cssText = `
        display: block;
        margin: 20px auto;
        padding: 10px 20px;
        background-color: #d4a853;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
    `;
    
    loadMoreBtn.addEventListener('click', loadMoreBeans);
    tableContainer.appendChild(loadMoreBtn);
}

function removeLoadMoreButton() {
    const existingBtn = document.getElementById('loadMoreBtn');
    if (existingBtn) {
        existingBtn.remove();
    }
}

async function loadMoreBeans() {
    // Prevent non-authenticated users from loading more
    if (!window.userLoggedIn) {
        return;
    }
    
    if (!currentFilters) return;
    
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.textContent = 'טוען...';
        loadMoreBtn.disabled = true;
    }
    
    try {
        currentPage++;
        currentFilters.page = currentPage;
        
        // Get API key from meta tag or use demo key
        let apiKey = null;
        const apiKeyMeta = document.querySelector('meta[name="api-key"]');
        if (apiKeyMeta) {
            apiKey = apiKeyMeta.getAttribute('content');
        } else {
            apiKey = 'demo-key-12345-67890-abcdef';
        }

        const response = await fetch('/api/search_beans', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(currentFilters)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const response_data = await response.json();
        const beans = response_data.data || response_data;
        
        if (beans.length === 0) {
            removeLoadMoreButton();
            return;
        }
        
        // Append new beans to existing table
        displayBeans(beans, false);
        
        // Check if there might be more results
        if (beans.length < parseInt(currentFilters.per_page)) {
            removeLoadMoreButton();
        } else {
            if (loadMoreBtn) {
                loadMoreBtn.textContent = 'טען פולים נוספים';
                loadMoreBtn.disabled = false;
            }
        }
        
    } catch (error) {
        console.error('Error loading more beans:', error);
        if (loadMoreBtn) {
            loadMoreBtn.textContent = 'שגיאה - נסה שוב';
            loadMoreBtn.disabled = false;
        }
    }
}

// Export additional functions to global scope
window.updateSortArrows = updateSortArrows;
window.removeLoadMoreButton = removeLoadMoreButton;
