@echo off
echo Hebrew Coffee Search System
echo ===========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found
    echo Creating .env file from template...
    copy .env.example .env
    echo.
    echo Please edit .env file and add your OPENROUTER_API_KEY
    echo Then run this script again
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Checking dependencies...
pip install -r requirements.txt >nul 2>&1

REM Run the Hebrew search script
echo Starting Hebrew Coffee Search...
echo.
python hebrew_coffee_search.py

pause
