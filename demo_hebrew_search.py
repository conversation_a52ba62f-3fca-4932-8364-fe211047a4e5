#!/usr/bin/env python3
"""
Demo script for Hebrew Coffee Search
This script demonstrates how the system works without requiring API keys
"""

import json
from hebrew_coffee_search import HebrewCoffeeSearch

def demo_translation_examples():
    """Show examples of how Hebrew queries would be translated to search criteria"""
    print("🇮🇱 Hebrew Coffee Search - Demo Mode")
    print("=" * 60)
    print("This demo shows how Hebrew queries are converted to search criteria")
    print("(No API keys required for this demonstration)")
    print()
    
    # Sample Hebrew queries and their expected translations
    examples = [
        {
            "hebrew": "קפה אתיופי עם טעמי פרחים",
            "english": "Ethiopian coffee with floral flavors",
            "expected_json": {
                "origin": ["אתיופיה"],
                "flavors": ["פרחוני"],
                "page": 1,
                "per_page": 10
            }
        },
        {
            "hebrew": "קפה ספיישלטי לאספרסו",
            "english": "Specialty coffee for espresso",
            "expected_json": {
                "speciality": 1,
                "brew_methods": ["espresso"],
                "page": 1,
                "per_page": 10
            }
        },
        {
            "hebrew": "קפה ברזילאי קלוי בינוני עם טעמי שוקולד",
            "english": "Brazilian medium roast coffee with chocolate flavors",
            "expected_json": {
                "origin": ["ברזיל"],
                "roast_level": "Medium",
                "flavors": ["שוקולד"],
                "page": 1,
                "per_page": 10
            }
        },
        {
            "hebrew": "קפה קולומביאני לפילטר עם טעמי פירות",
            "english": "Colombian filter coffee with fruity flavors",
            "expected_json": {
                "origin": ["קולומביה"],
                "brew_methods": ["pour_over"],
                "flavors": ["פירותיים"],
                "page": 1,
                "per_page": 10
            }
        },
        {
            "hebrew": "קפה דקף עם טעמי אגוזים",
            "english": "Decaf coffee with nutty flavors",
            "expected_json": {
                "decaf": 1,
                "flavors": ["אגוזים"],
                "page": 1,
                "per_page": 10
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"📝 Example {i}:")
        print(f"   Hebrew: {example['hebrew']}")
        print(f"   English: {example['english']}")
        print(f"   Expected JSON:")
        print(f"   {json.dumps(example['expected_json'], ensure_ascii=False, indent=6)}")
        print()

def demo_available_options():
    """Show available search options from the JSON files"""
    print("📋 Available Search Options")
    print("-" * 40)
    
    search_system = HebrewCoffeeSearch()
    
    if search_system.available_flavors:
        print(f"🎯 Flavors ({len(search_system.available_flavors)} available):")
        # Show flavors in groups of 5
        for i in range(0, min(20, len(search_system.available_flavors)), 5):
            flavors_group = search_system.available_flavors[i:i+5]
            print(f"   {', '.join(flavors_group)}")
        if len(search_system.available_flavors) > 20:
            print(f"   ... and {len(search_system.available_flavors) - 20} more")
        print()
    
    if search_system.available_origins:
        print(f"🌍 Origins ({len(search_system.available_origins)} available):")
        # Show origins in groups of 5
        for i in range(0, min(15, len(search_system.available_origins)), 5):
            origins_group = search_system.available_origins[i:i+5]
            print(f"   {', '.join(origins_group)}")
        if len(search_system.available_origins) > 15:
            print(f"   ... and {len(search_system.available_origins) - 15} more")
        print()
    
    if search_system.available_cities:
        print(f"🏙️ Israeli Cities ({len(search_system.available_cities)} available):")
        # Show cities in groups of 4
        for i in range(0, min(12, len(search_system.available_cities)), 4):
            cities_group = search_system.available_cities[i:i+4]
            print(f"   {', '.join(cities_group)}")
        if len(search_system.available_cities) > 12:
            print(f"   ... and {len(search_system.available_cities) - 12} more")
        print()

def demo_search_parameters():
    """Show all possible search parameters"""
    print("⚙️ All Search Parameters")
    print("-" * 30)
    
    parameters = {
        "origin": "Array of origin countries (in Hebrew)",
        "flavors": "Array of flavor notes (in Hebrew)",
        "processing": "Processing method (Washed, Natural, Honey, Semi-washed)",
        "roast_level": "Roast level (Light, Medium, Dark, Extra Dark)",
        "elevation": "Elevation codes (altd_1, altd_2, altd_3, altd_4)",
        "arabica": "1 for Arabica beans, 0 otherwise",
        "robusta": "1 for Robusta beans, 0 otherwise",
        "singleorigin": "1 for single origin, 0 otherwise",
        "mix": "1 for blends, 0 otherwise",
        "speciality": "1 for specialty coffee, 0 otherwise",
        "decaf": "1 for decaffeinated, 0 otherwise",
        "brew_methods": "Array of brew methods (turkish, espresso, french_press, pour_over, drip, cold_brew)",
        "cities": "Array of Israeli cities (in Hebrew)",
        "page": "Page number for pagination",
        "per_page": "Results per page (max 100)"
    }
    
    for param, description in parameters.items():
        print(f"   • {param}: {description}")
    print()

def demo_sample_results():
    """Show what typical search results look like"""
    print("📊 Sample Search Results")
    print("-" * 30)
    
    sample_results = {
        "data": [
            {
                "bean_name": "Ethiopian Yirgacheffe",
                "roaster_name": "Jerusalem Coffee Roasters",
                "origin": "אתיופיה",
                "flavors": "פרחוני, הדרים, תה ירוק",
                "roast_level": "Light",
                "price": 89.0,
                "weight": 250,
                "arabica": 100,
                "robusta": 0,
                "speciality": 1,
                "SCA_score": 87.5,
                "roaster_webpage": "https://example-roaster.com"
            },
            {
                "bean_name": "Brazilian Santos",
                "roaster_name": "Tel Aviv Coffee Co.",
                "origin": "ברזיל",
                "flavors": "שוקולד, אגוזים, קרמל",
                "roast_level": "Medium",
                "price": 65.0,
                "weight": 250,
                "arabica": 80,
                "robusta": 20,
                "speciality": 0,
                "SCA_score": None,
                "roaster_webpage": "https://example-roaster2.com"
            }
        ],
        "pagination": {
            "page": 1,
            "per_page": 10,
            "total": 2,
            "total_pages": 1
        }
    }
    
    print("Example results for 'קפה אתיופי עם טעמי פרחים':")
    print()
    
    for i, bean in enumerate(sample_results["data"], 1):
        print(f"☕ {i}. {bean['bean_name']}")
        print(f"   🏭 Roaster: {bean['roaster_name']}")
        print(f"   🌍 Origin: {bean['origin']}")
        print(f"   🎯 Flavors: {bean['flavors']}")
        print(f"   🔥 Roast Level: {bean['roast_level']}")
        print(f"   💰 Price: ₪{bean['price']} ({bean['weight']}g)")
        print(f"   🌱 {bean['arabica']}% Arabica", end="")
        if bean['robusta']:
            print(f", {bean['robusta']}% Robusta")
        else:
            print()
        if bean['speciality']:
            print("   ⭐ Specialty Grade")
        if bean['SCA_score']:
            print(f"   📊 SCA Score: {bean['SCA_score']}")
        print(f"   🔗 {bean['roaster_webpage']}")
        print()

def main():
    """Run the demo"""
    demo_translation_examples()
    demo_available_options()
    demo_search_parameters()
    demo_sample_results()
    
    print("🚀 Ready to try the real thing?")
    print("-" * 35)
    print("1. Set up your OpenRouter API key in .env file")
    print("2. Run: python hebrew_coffee_search.py")
    print("3. Start searching in Hebrew!")
    print()
    print("For testing without API keys, run: python test_hebrew_search.py")

if __name__ == "__main__":
    main()
