# Environment variables
.env
.env.*
!.env.example

# Firebase credentials
instance/serviceAccountKey.json
**/serviceAccountKey*.json

# Python files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
dev_scripts/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
logs/
ai_dev/
*.egg-info/
.installed.cfg
*.egg

# Project specific files
extract_cities.py
extract_flavors.py
import_coffee.py
update_specific_field.py
dup.py
coffee_data.csv
check_db_schema.py
check_schema.py
database_setup.py
checkenv.py
add_specific_field.py
israeli_coffee_letter.html
israeli_coffee_letter.txt
utils/roasters*.csv
utils/roaster_links.py
edit_price.html
encrypt_password_util.py
coffee_database.db
docs/security_remediation_plan.md
test_roaster_insert.py
create_poll_table.py
scripts/extract_firebase.py
firebase_users.csv
general.db
view_poll_records.py
general.db
check_poll_db.py
scripts/visualize_bean_prices.py
scripts/histogram_bean_prices
.vscode/settings.json
.roomodes

# IDE settings
.vscode/
.idea/
*.swp
*.swo
docs/security_remediation_plan.md

EMAIL_MIGRATION_GUIDE.md



scripts/debug_database_insert.py
scripts/debug_database_insert.py
scripts/fix_database_permissions.sh
Coffee_project.code-workspace
