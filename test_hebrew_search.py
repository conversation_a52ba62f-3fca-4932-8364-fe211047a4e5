#!/usr/bin/env python3
"""
Test script for the Hebrew Coffee Search system
This script tests the components without requiring user interaction
"""

import json
import os
from dotenv import load_dotenv
from hebrew_coffee_search import HebrewCoffeeSearch

def test_search_options_loading():
    """Test if the JSON search options files can be loaded"""
    print("🧪 Testing search options loading...")
    
    search_system = HebrewCoffeeSearch()
    
    if search_system.available_flavors:
        print(f"✅ Loaded {len(search_system.available_flavors)} flavors")
        print(f"   Sample flavors: {search_system.available_flavors[:5]}")
    else:
        print("❌ Failed to load flavors")
    
    if search_system.available_origins:
        print(f"✅ Loaded {len(search_system.available_origins)} origins")
        print(f"   Sample origins: {search_system.available_origins[:5]}")
    else:
        print("❌ Failed to load origins")
    
    if search_system.available_cities:
        print(f"✅ Loaded {len(search_system.available_cities)} cities")
        print(f"   Sample cities: {search_system.available_cities[:5]}")
    else:
        print("❌ Failed to load cities")

def test_prompt_creation():
    """Test the prompt creation for the LLM"""
    print("\n🧪 Testing prompt creation...")
    
    search_system = HebrewCoffeeSearch()
    test_hebrew = "קפה אתיופי עם טעמי פרחים"
    
    prompt = search_system.create_search_prompt(test_hebrew)
    
    if prompt and len(prompt) > 100:
        print("✅ Prompt created successfully")
        print(f"   Prompt length: {len(prompt)} characters")
        print(f"   Contains Hebrew input: {'✅' if test_hebrew in prompt else '❌'}")
        print(f"   Contains JSON example: {'✅' if 'origin' in prompt else '❌'}")
    else:
        print("❌ Failed to create prompt")

def test_environment_setup():
    """Test if environment variables are properly set"""
    print("\n🧪 Testing environment setup...")
    
    load_dotenv()
    
    openrouter_key = os.getenv('OPENROUTER_API_KEY')
    coffee_key = os.getenv('COFFEE_API_KEY')
    coffee_url = os.getenv('COFFEE_API_URL')
    
    print(f"OpenRouter API Key: {'✅ Set' if openrouter_key else '❌ Not set'}")
    print(f"Coffee API Key: {'✅ Set' if coffee_key else '⚠️  Using default'}")
    print(f"Coffee API URL: {'✅ Set' if coffee_url else '⚠️  Using default'}")
    
    if openrouter_key:
        print(f"   OpenRouter key preview: {openrouter_key[:10]}...")
    else:
        print("   ⚠️  Set OPENROUTER_API_KEY in .env file for full functionality")

def test_json_parsing():
    """Test JSON parsing with sample LLM responses"""
    print("\n🧪 Testing JSON parsing...")
    
    # Test valid JSON response
    sample_json = '''
    {
        "origin": ["אתיופיה"],
        "flavors": ["פרחוני"],
        "speciality": 1,
        "page": 1,
        "per_page": 10
    }
    '''
    
    try:
        parsed = json.loads(sample_json)
        print("✅ JSON parsing works correctly")
        print(f"   Parsed fields: {list(parsed.keys())}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
    
    # Test JSON with markdown code blocks (common LLM response format)
    sample_markdown_json = '''```json
    {
        "origin": ["ברזיל"],
        "flavors": ["שוקולד"],
        "brew_methods": ["espresso"],
        "page": 1,
        "per_page": 10
    }
    ```'''
    
    try:
        # Simulate the cleaning process from the main script
        content = sample_markdown_json.strip()
        if content.startswith('```'):
            content = content.split('```')[1]
            if content.startswith('json'):
                content = content[4:]
        
        parsed = json.loads(content)
        print("✅ Markdown JSON parsing works correctly")
        print(f"   Parsed fields: {list(parsed.keys())}")
    except json.JSONDecodeError as e:
        print(f"❌ Markdown JSON parsing failed: {e}")

def test_sample_search_criteria():
    """Test with sample search criteria to see if they're valid"""
    print("\n🧪 Testing sample search criteria...")
    
    sample_criteria = [
        {
            "origin": ["אתיופיה"],
            "flavors": ["פרחוני"],
            "page": 1,
            "per_page": 5
        },
        {
            "speciality": 1,
            "brew_methods": ["espresso"],
            "page": 1,
            "per_page": 5
        },
        {
            "origin": ["ברזיל"],
            "flavors": ["שוקולד"],
            "roast_level": "Medium",
            "page": 1,
            "per_page": 5
        }
    ]
    
    for i, criteria in enumerate(sample_criteria, 1):
        print(f"   Sample {i}: {criteria}")
        
        # Check if criteria has required fields
        has_page = 'page' in criteria
        has_per_page = 'per_page' in criteria
        has_search_params = any(key in criteria for key in ['origin', 'flavors', 'speciality', 'brew_methods'])
        
        if has_page and has_per_page and has_search_params:
            print(f"   ✅ Sample {i} is valid")
        else:
            print(f"   ❌ Sample {i} is missing required fields")

def run_all_tests():
    """Run all tests"""
    print("🧪 Hebrew Coffee Search - Test Suite")
    print("=" * 50)
    
    test_environment_setup()
    test_search_options_loading()
    test_prompt_creation()
    test_json_parsing()
    test_sample_search_criteria()
    
    print("\n" + "=" * 50)
    print("🏁 Test suite completed!")
    print("\nNext steps:")
    print("1. If all tests pass, try running: python hebrew_coffee_search.py")
    print("2. If OpenRouter API key is missing, add it to .env file")
    print("3. Make sure the coffee API server is running for full functionality")

if __name__ == "__main__":
    run_all_tests()
