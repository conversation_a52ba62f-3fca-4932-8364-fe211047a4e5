#!/usr/bin/env python3
"""
Quick test of the Hebrew Coffee Search with Claude 3.5 Sonnet
"""

from hebrew_coffee_search import HebrewCoffeeSearch

def test_full_flow():
    """Test the complete flow with a sample Hebrew query"""
    
    print("🧪 Testing Hebrew Coffee Search with Claude 3.5 Sonnet")
    print("=" * 60)
    
    search_system = HebrewCoffeeSearch()
    
    # Test queries
    test_queries = [
        "קפה אתיופי עם טעמי פרחים",
        "קפה ספיישלטי לאספרסו", 
        "קפה ברזילאי עם טעמי שוקולד"
    ]
    
    for i, hebrew_query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {hebrew_query}")
        print("-" * 40)
        
        # Step 1: Create prompt
        prompt = search_system.create_search_prompt(hebrew_query)
        print(f"✅ Prompt created ({len(prompt)} chars)")
        
        # Step 2: Query AI
        search_criteria = search_system.query_openrouter(prompt)
        
        if search_criteria:
            print(f"✅ AI conversion successful")
            print(f"   Generated criteria: {search_criteria}")
            
            # Step 3: Test API search (this might fail if API server isn't running)
            print("🔍 Testing API search...")
            results = search_system.search_coffee_beans(search_criteria)
            
            if results:
                print(f"✅ API search successful")
                beans_count = len(results.get('data', []))
                total_count = results.get('pagination', {}).get('total', beans_count)
                print(f"   Found {total_count} total results, showing {beans_count}")
                
                # Show first result if available
                if results.get('data'):
                    first_bean = results['data'][0]
                    print(f"   Sample result: {first_bean.get('bean_name', 'Unknown')} from {first_bean.get('roaster_name', 'Unknown')}")
            else:
                print("⚠️  API search failed (server might not be running)")
                print("   This is expected if the Flask app isn't running")
        else:
            print("❌ AI conversion failed")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\nIf AI conversion worked but API search failed:")
    print("1. Make sure your Flask app is running (python app.py)")
    print("2. Check that the API endpoint is accessible")
    print("3. The Hebrew search script should still work interactively")

if __name__ == "__main__":
    test_full_flow()
