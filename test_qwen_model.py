#!/usr/bin/env python3
"""
Test script specifically for the Qwen model to debug the empty response issue
"""

import json
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def test_qwen_model():
    """Test the Qwen model with a simple Hebrew coffee query"""
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found")
        return
    
    # Test with the actual complex prompt from the main script
    prompt = """
You are a coffee search expert. Convert the following Hebrew text into JSON search criteria for a coffee beans database.

Hebrew input: "קפה אתיופי עם טעמי פרחים"

Available options:
- Flavors (טעמים): אגוזי לוז, אגוזים, אגס, אדמתיים, אוכמניות... (and more)
- Origins (מקור): אוגנדה, אוסטרליה, אינדונזיה, אל סלבדור, אקוודור... (and more)
- Cities (ערים): א. ת. עמק חפר, בית ג'אן, בית שמש, בני ברק... (and more)
- Processing methods: Washed, Natural, Honey, Semi-washed
- Roast levels: Light, Medium, Dark, Extra Dark
- Elevation: altd_1 (0-1000m), altd_2 (1001-1500m), altd_3 (1501-2000m), altd_4 (>2000m)
- Brew methods: turkish, espresso, french_press, pour_over, drip, cold_brew

IMPORTANT: You must return ONLY a valid JSON object. Do not include any explanations, markdown formatting, or additional text. Just the raw JSON.

Return a JSON object with these possible fields:
{
  "origin": [],           // Array of origin countries (in Hebrew)
  "flavors": [],          // Array of flavor notes (in Hebrew)
  "processing": "",       // Single processing method
  "roast_level": "",      // Single roast level
  "elevation": [],        // Array of elevation codes
  "arabica": 0,           // 1 if specifically wants Arabica, 0 otherwise
  "robusta": 0,           // 1 if specifically wants Robusta, 0 otherwise
  "singleorigin": 0,      // 1 if wants single origin, 0 otherwise
  "mix": 0,               // 1 if wants blends, 0 otherwise
  "speciality": 0,        // 1 if wants specialty coffee, 0 otherwise
  "decaf": 0,             // 1 if wants decaf, 0 otherwise
  "brew_methods": [],     // Array of brew methods
  "cities": [],           // Array of Israeli cities (in Hebrew)
  "page": 1,
  "per_page": 10
}

Guidelines:
- Only include fields that are relevant to the Hebrew input
- Use exact Hebrew terms from the available options
- If user mentions quality/premium/specialty, set speciality: 1
- If user mentions specific brewing method, include in brew_methods
- If user mentions origin countries, include in origin array
- If user mentions flavors/taste notes, include in flavors array
- Set reasonable defaults for page (1) and per_page (10)

Example Hebrew inputs and responses:
"קפה אתיופי עם טעמי פרחים" → {"origin": ["אתיופיה"], "flavors": ["פרחוני"], "page": 1, "per_page": 10}
"קפה ספיישלטי לאספרסו" → {"speciality": 1, "brew_methods": ["espresso"], "page": 1, "per_page": 10}
"""
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test different models
    models_to_test = [
        "qwen/qwen3-235b-a22b-thinking-2507",
        "anthropic/claude-3.5-sonnet",
        "openai/gpt-4o-mini"
    ]
    
    for model in models_to_test:
        print(f"\n🧪 Testing {model}")
        print("-" * 50)
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response structure: {list(result.keys())}")
                
                if 'choices' in result and result['choices']:
                    content = result['choices'][0]['message']['content']
                    print(f"Content length: {len(content) if content else 0}")
                    print(f"Content preview: {content[:200] if content else 'EMPTY'}")
                    
                    if content:
                        try:
                            # Try to parse as JSON
                            parsed = json.loads(content.strip())
                            print("✅ Valid JSON response")
                            print(f"Parsed: {parsed}")
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON parsing failed: {e}")
                    else:
                        print("❌ Empty content")
                else:
                    print("❌ No choices in response")
                    print(f"Full response: {result}")
            else:
                print(f"❌ API Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_qwen_model()
