# Hebrew Coffee Search Script

This script allows users to search for coffee beans using natural Hebrew language input. The script uses AI to translate and convert Hebrew queries into structured search criteria for the coffee beans API.

## Features

- 🇮🇱 **Hebrew Language Support**: Enter search queries in natural Hebrew
- 🤖 **AI-Powered Translation**: Uses OpenRouter.ai to convert Hebrew text to search criteria
- ☕ **Comprehensive Search**: Searches across flavors, origins, roast levels, and more
- 📊 **Detailed Results**: Shows coffee details including price, roaster, and characteristics
- 🔄 **Interactive Mode**: Continuous search session with multiple queries

## Setup

### 1. Install Dependencies

Make sure you have Python 3.7+ installed, then install the required packages:

```bash
pip install -r requirements.txt
```

### 2. Get API Keys

#### OpenRouter.ai API Key (Required)
1. Go to [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for an account
3. Get your API key from the dashboard
4. The script uses Claude 3.5 Sonnet as the primary model for excellent Hebrew language support
5. Fallback models include GPT-4o and Llama if needed

#### Coffee API Key (Optional)
- The script uses a demo key by default
- For production use, contact the system administrator for a proper API key

### 3. Configure Environment Variables

Copy the example environment file and add your API keys:

```bash
cp .env.example .env
```

Edit the `.env` file and add your OpenRouter API key:

```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
COFFEE_API_KEY=demo-key-12345-67890-abcdef  # Optional: use your own key
COFFEE_API_URL=http://localhost:5000/api/search_beans  # Adjust if needed
```

## Usage

### Running the Script

```bash
python hebrew_coffee_search.py
```

### Example Searches

The script accepts natural Hebrew language queries. Here are some examples:

#### Basic Searches
- `קפה אתיופי` - Ethiopian coffee
- `קפה ברזילאי` - Brazilian coffee
- `קפה קולומביאני` - Colombian coffee

#### Flavor-Based Searches
- `קפה עם טעמי שוקולד` - Coffee with chocolate flavors
- `קפה פרחוני` - Floral coffee
- `קפה עם טעמי פירות` - Coffee with fruity flavors
- `קפה עם טעמי אגוזים` - Coffee with nutty flavors

#### Specialty Searches
- `קפה ספיישלטי` - Specialty coffee
- `קפה לאספרסו` - Coffee for espresso
- `קפה לפילטר` - Coffee for filter/pour-over
- `קפה קלוי בינוני` - Medium roast coffee
- `קפה קלוי כהה` - Dark roast coffee

#### Complex Searches
- `קפה אתיופי ספיישלטי עם טעמי פרחים` - Ethiopian specialty coffee with floral notes
- `קפה ברזילאי לאספרסו עם טעמי שוקולד` - Brazilian espresso coffee with chocolate flavors
- `קפה קלוי בינוני מקולומביה` - Medium roast coffee from Colombia

### Search Results

The script displays detailed information for each matching coffee:

- ☕ **Bean Name**: The specific name of the coffee
- 🏭 **Roaster**: The roasting company
- 🌍 **Origin**: Country/region of origin
- 🎯 **Flavors**: Tasting notes and flavor profile
- 🔥 **Roast Level**: Light, Medium, Dark, etc.
- 💰 **Price**: Cost in Israeli Shekels and weight
- 🌱 **Bean Type**: Arabica/Robusta percentages
- ⭐ **Specialty Grade**: If it's specialty coffee
- 📊 **SCA Score**: Specialty Coffee Association score (if available)
- 🔗 **Website**: Roaster's website

## How It Works

1. **User Input**: You enter a search query in Hebrew
2. **AI Processing**: The script sends your query to OpenRouter.ai (Claude 3.5 Sonnet)
3. **Translation**: The AI translates and converts your Hebrew text into structured JSON search criteria
4. **API Search**: The generated criteria are used to search the coffee beans database
5. **Results Display**: Matching coffee beans are displayed with detailed information

## Available Search Options

The script can search across these parameters:

### Origins (מקור)
- אתיופיה (Ethiopia)
- ברזיל (Brazil)
- קולומביה (Colombia)
- קניה (Kenya)
- גואטמלה (Guatemala)
- And many more...

### Flavors (טעמים)
- שוקולד (Chocolate)
- פרחוני (Floral)
- פירותיים (Fruity)
- אגוזים (Nutty)
- קרמל (Caramel)
- הדרים (Citrus)
- And many more...

### Processing Methods
- Washed
- Natural
- Honey
- Semi-washed

### Roast Levels
- Light
- Medium
- Dark
- Extra Dark

### Brew Methods
- Turkish
- Espresso
- French Press
- Pour Over
- Drip
- Cold Brew

## Troubleshooting

### Common Issues

1. **"OpenRouter API key not found"**
   - Make sure you've set `OPENROUTER_API_KEY` in your `.env` file
   - Verify your API key is correct

2. **"Failed to process your search"**
   - Check your internet connection
   - Verify your OpenRouter API key is valid and has credits
   - Try a simpler search query

3. **"Failed to search coffee beans"**
   - Check if the coffee API server is running
   - Verify the `COFFEE_API_URL` in your `.env` file
   - Check if the API key is valid

4. **No results found**
   - Try broader search terms
   - Check if the database has coffee matching your criteria
   - Try searching in English as a test

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify all environment variables are set correctly
3. Test with simple queries first
4. Check your API key credits and limits

## API Costs

- OpenRouter.ai charges per token used
- Claude 3.5 Sonnet costs approximately $3 per million input tokens
- Each search query typically uses 200-500 tokens
- Monitor your usage on the OpenRouter dashboard

## Security Notes

- Keep your API keys secure and never commit them to version control
- Use environment variables for all sensitive configuration
- The demo coffee API key has rate limits - get a proper key for production use
